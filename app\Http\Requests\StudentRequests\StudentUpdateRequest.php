<?php

namespace App\Http\Requests\StudentRequests;

use App\Enums\GenderEnum;
use App\Enums\UserStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $studentId = $this->route('id');

        return [
            // User data
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($this->getUserIdForStudent($studentId)),
            ],
            'password' => ['sometimes', 'nullable', 'string', 'min:8'],
            'status' => ['sometimes', 'integer', Rule::in(UserStatus::values())],

            // Student profile data
            'nis' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('students', 'nis')->ignore($studentId),
            ],
            'nisn' => [
                'sometimes',
                'string',
                'max:20',
                Rule::unique('students', 'nisn')->ignore($studentId),
            ],
            'birth_place' => ['sometimes', 'string', 'max:100'],
            'birth_date' => ['sometimes', 'date', 'before:today'],
            'gender' => ['sometimes', Rule::enum(GenderEnum::class)],
            'parent_name' => ['sometimes', 'string', 'max:255'],
            'parent_phone' => ['sometimes', 'string', 'max:20', 'regex:/^[0-9+\-\s()]+$/'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama lengkap',
            'email' => 'Email',
            'password' => 'Password',
            'status' => 'Status',
            'nis' => 'NIS',
            'nisn' => 'NISN',
            'birth_place' => 'Tempat lahir',
            'birth_date' => 'Tanggal lahir',
            'gender' => 'Jenis kelamin',
            'parent_name' => 'Nama orang tua/wali',
            'parent_phone' => 'Nomor telepon orang tua/wali',
        ];
    }

    /**
     * Get user ID for the student through service layer
     */
    private function getUserIdForStudent(?int $studentId): ?int
    {
        if ($studentId === null) {
            return null;
        }

        try {
            $studentService = app(\App\Services\StudentService::class);
            $student = $studentService->findById($studentId);
            return $student->user_id;
        } catch (\Exception $e) {
            return null;
        }
    }
}
