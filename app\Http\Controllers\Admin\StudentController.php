<?php

namespace App\Http\Controllers\Admin;

use App\Services\ClassroomService;
use App\Services\StudentEnrollmentService; // Tetap ada jika ada fitur spesifik yang dicolok langsung
use App\Services\StudentRegistrationService; // Tetap ada jika ada fitur spesifik yang dicolok langsung
use App\Services\StudentService;
use App\Services\UserService; // Tetap ada untuk injeksi, tapi utamanya di StudentService
use App\Enums\GenderEnum;
use App\Enums\ReligionEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException; // Tetap di-import
use App\Exports\StudentExport;
use App\Exports\StudentTemplateExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\StudentRequests\StudentStoreRequest;
use App\Http\Requests\StudentRequests\StudentUpdateRequest;
use App\Imports\StudentImport;
use App\Models\AcademicYear;
use App\Models\Classroom; // Digunakan di enrollMultiple, show
use App\Models\Student; // Digunakan di show, edit (route model binding)
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage; // Digunakan untuk hapus gambar
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response; // Untuk kode status HTTP
use Throwable; // Untuk catch generic error, tapi di Controller harusnya minimal

class StudentController extends Controller
{
    /**
     * Student service instance
     */
    protected StudentService $studentService;

    /**
     * StudentRegistrationService instance (jika masih digunakan langsung oleh controller)
     */
    protected StudentRegistrationService $studentRegistrationService; // Mungkin bisa dihapus jika semua dihandle StudentService

    /**
     * StudentEnrollmentService instance (jika masih digunakan langsung oleh controller)
     */
    protected StudentEnrollmentService $studentEnrollmentService; // Mungkin bisa dihapus jika semua dihandle StudentService

    /**
     * User service instance (jika masih digunakan langsung oleh controller)
     */
    protected UserService $userService; // Mungkin bisa dihapus jika semua dihandle StudentService

    /**
     * Classroom service instance
     */
    protected ClassroomService $classroomService;

    public function __construct(
        StudentService $studentService,
        StudentRegistrationService $studentRegistrationService, // Dipertahankan, tapi perannya diperiksa
        StudentEnrollmentService $studentEnrollmentService, // Dipertahankan, tapi perannya diperiksa
        UserService $userService, // Dipertahankan, tapi perannya diperiksa
        ClassroomService $classroomService
    ) {
        $this->studentService = $studentService;
        $this->studentRegistrationService = $studentRegistrationService;
        $this->studentEnrollmentService = $studentEnrollmentService;
        $this->userService = $userService;
        $this->classroomService = $classroomService;
    }

    /**
     * Display a listing of students.
     */
    public function index(Request $request): View|JsonResponse
    {
        // Pindahkan logika DataTables ke class terpisah jika menggunakan Yajra DataTables Class
        // Atau ke trait/helper jika ingin memformat manual
        if ($request->ajax()) {
            $students = $this->studentService->getAllWithRelations(['user', 'classrooms'], $request->all());
            return $this->formatStudentsForDatatable($students);
        }

        $classrooms = $this->classroomService->getAllClassrooms();

        return view('admin.pages.student.index', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'classrooms' => $classrooms,
        ]);
    }

    /**
     * Format response for DataTables. Pindahkan ini ke Trait/Yajra DataTables Class.
     */
    public function formatStudentsForDatatable($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->addColumn('gender', fn($row) => $row->gender?->label() ?? '-') // Gunakan label() dari enum
            ->addColumn('birth_date', fn($row) => $row->birth_date ? date('d/m/Y', strtotime($row->birth_date)) : '-')
            ->addColumn('classrooms', function ($row) {
                // Logika ini harusnya ada di model Student atau di StudentService jika sangat kompleks
                // Tapi untuk DataTables, bisa saja di sini jika ingin output HTML spesifik
                $classrooms = $row->classrooms()->withActiveAcademicYear()->get(); // Pastikan withActiveAcademicYear ada di Classroom model atau scope
                $html = '';
                if ($classrooms->count() > 0) {
                    foreach ($classrooms as $classroom) {
                        $html .= '<span class="badge bg-primary-subtle text-primary">' . $classroom->name . '</span> ';
                    }
                } else {
                    $html = '<span class="badge bg-warning-subtle text-warning">Belum ada kelas</span>';
                }
                return $html;
            })
            ->addColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.components.student-actions', [
                    'id' => $row->id,
                    'editUrl' => route('admin.students.edit', $row->id),
                    'viewUrl' => route('admin.students.show', $row->id),
                    'enrollUrl' => route('admin.students.enroll', $row->id),
                    'deleteUrl' => route('admin.students.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['action', 'classrooms', 'status'])
            ->make(true);
    }

    /**
     * Show the form for creating a new student.
     */
    public function create(): View
    {
        return view('admin.pages.student.create', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'religions' => ReligionEnum::options(),
        ]);
    }

    /**
     * Store a newly created student.
     */
    public function store(StudentStoreRequest $request): JsonResponse
    {
        // Handle profile picture upload di sini sebelum panggil service
        $data = $request->validated();
        if ($request->hasFile('profile_picture')) {
            $data['profile_picture'] = $request->file('profile_picture')->store('students/profile-pictures', 'public');
        }

        $this->studentService->create($data); // Panggil StudentService

        return response()->json([
            'success' => true,
            'message' => 'Data siswa berhasil ditambahkan.',
        ], Response::HTTP_CREATED);
    }

    /**
     * Display the specified student.
     */
    public function show(Student $student): View
    {
        // Menggunakan Route Model Binding, load relasi yang diperlukan di sini atau di service.
        $student->load([
            'user',
            'classrooms' => function ($query) {
                $query->with('academicYear');
            },
        ]);

        $academicYears = AcademicYear::where('status', 'active')->get(); // Ini bisa diambil dari ClassroomService atau AcademicYearService
        // $classrooms di sini sudah ada di $student->classrooms dari eager loading

        return view('admin.pages.student.show', [
            'student' => $student,
            'classrooms' => $student->classrooms, // Ambil dari model yang sudah di-load
            'academicYears' => $academicYears,
        ]);
    }

    /**
     * Show the form for editing the specified student.
     */
    public function edit(int $id): View
    {
        $student = $this->studentService->findById($id); // StudentService akan melempar BusinessLogicException
        $student->load('user'); // Pastikan user di-load

        return view('admin.pages.student.edit', [
            'student' => $student,
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'religions' => ReligionEnum::options(),
        ]);
    }

    /**
     * Update the specified student.
     */
    public function update(StudentUpdateRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();

        $this->studentService->update($id, $data); // Panggil StudentService

        return response()->json([
            'success' => true,
            'message' => 'Data siswa berhasil diperbarui.',
        ]);
    }

    /**
     * Remove the specified student.
     */
    public function destroy(int $id): JsonResponse
    {
        $this->studentService->delete($id); // Panggil StudentService

        return response()->json([
            'success' => true,
            'message' => 'Data siswa berhasil dihapus.',
        ]);
    }

    /**
     * Enroll student to a classroom.
     */
    public function enroll(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'classroom_id' => 'required|exists:classrooms,id',
            'academic_year_id' => 'required|exists:academic_years,id',
        ]);

        $this->studentService->enrollStudentToClassroom(
            $id,
            $request->classroom_id,
            $request->academic_year_id
        );

        return response()->json([
            'success' => true,
            'message' => 'Siswa berhasil didaftarkan ke kelas.',
        ]);
    }

    /**
     * Import students data from Excel.
     */
    public function import(Request $request): JsonResponse
    {
        $request->validate([
            'import_file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
        ]);

        // StudentService yang akan mengelola transaksi dan penanganan error import
        $result = $this->studentService->importStudents($request->file('import_file'));

        if (!empty($result['errors'])) {
            return response()->json([
                'success' => false,
                'message' => 'Beberapa data siswa gagal diimpor karena kesalahan validasi.',
                'errors' => $result['errors'],
            ], Response::HTTP_UNPROCESSABLE_ENTITY); // 422 Unprocessable Entity
        }

        return response()->json([
            'success' => true,
            'message' => 'Data siswa berhasil diimpor.',
        ]);
    }

    /**
     * Export students data to Excel.
     */
    public function export(Request $request)
    {
        // Langsung panggil Excel::download, ini adalah tugas export, bukan logika bisnis
        $filters = $request->all();
        return Excel::download(
            new StudentExport($filters),
            'daftar-siswa-' . date('Y-m-d-His') . '.xlsx'
        );
    }

    /**
     * Download import template.
     */
    public function template()
    {
        // Langsung panggil Excel::download
        return Excel::download(
            new StudentTemplateExport(),
            'template-impor-siswa.xlsx'
        );
    }

    /**
     * Enroll multiple students to a classroom.
     */
    public function enrollMultiple(Request $request): JsonResponse
    {
        $request->validate([
            'classroom_id' => 'required|exists:classrooms,id',
            'student_ids' => 'required|array',
            'student_ids.*' => 'exists:students,id',
        ]);

        $this->studentService->bulkEnrollToClassroom(
            $request->student_ids,
            $request->classroom_id
        );

        return response()->json([
            'success' => true,
            'message' => 'Siswa berhasil ditambahkan ke kelas.',
        ]);
    }

    /**
     * Get available students for a classroom.
     */
    public function getAvailableStudents(Request $request, Classroom $classroom): JsonResponse
    {
        // Pindahkan logika query ke service/repository
        $studentsData = $this->studentService->getStudentsNotEnrolledInClassroom(
            $classroom->id,
            $classroom->academic_year_id, // Asumsi Classroom model sudah memiliki academic_year_id
            $request->search ?? null,
            $request->page ?? 1,
            $request->perPage ?? 10
        );

        return response()->json([
            'data' => $studentsData['data'],
            'current_page' => $studentsData['current_page'],
            'last_page' => $studentsData['last_page'],
            'total' => $studentsData['total'],
        ]);
    }

    /**
     * Remove a student from a classroom.
     */
    public function removeFromClassroom(Request $request): JsonResponse
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
            'classroom_id' => 'required|exists:classrooms,id',
        ]);

        $this->studentService->removeStudentFromClassroom(
            $request->student_id,
            $request->classroom_id
        );

        return response()->json([
            'success' => true,
            'message' => 'Siswa berhasil dikeluarkan dari kelas.',
        ]);
    }

    /**
     * Change the status of a student's account.
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'status' => 'required|boolean',
        ]);

        $updatedStudent = $this->studentService->changeStudentStatus($id, $request->status);

        $statusLabel = $updatedStudent->user->status->label();

        return response()->json([
            'success' => true,
            'message' => "Status siswa berhasil diubah menjadi {$statusLabel}.",
        ]);
    }

    /**
     * Update student account settings.
     */
    public function updateAccount(Request $request, int $id): JsonResponse
    {
        // Gunakan FormRequest terpisah jika validasi lebih kompleks
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $id . ',id', // Pastikan $id adalah user_id dari student
            'password' => 'nullable|string|min:8',
        ]);

        $this->studentService->updateStudentAccount($id, $request->only(['name', 'email', 'password']));

        return response()->json([
            'success' => true,
            'message' => 'Akun siswa berhasil diperbarui.',
        ]);
    }
}
