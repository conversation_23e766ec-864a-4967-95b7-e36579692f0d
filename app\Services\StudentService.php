<?php

namespace App\Services;

use App\Repositories\Contracts\StudentRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface; // Dipertahankan, tapi jarang dipanggil langsung
use App\Services\UserService; // Ini yang utama untuk User
use App\Services\ClassroomService; // Mungkin dibutuhkan untuk validasi Classroom kapasitas
use App\Enums\UserStatus;
use App\Models\Student;
use App\Models\AcademicYear; // Untuk validasi AcademicYear
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage; // Untuk hapus gambar profil
use Maatwebsite\Excel\Facades\Excel; // Untuk import/export
use App\Imports\StudentImport; // Kelas import Excel
use App\Exceptions\BusinessLogicException;
use App\Exceptions\StudentException; // Exception spesifik siswa
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Throwable;

class StudentService
{
    protected StudentRepositoryInterface $studentRepository;
    protected UserRepositoryInterface $userRepository; // Digunakan sangat jarang, UserService lebih diutamakan
    protected UserService $userService; // Untuk operasi User dasar
    protected ClassroomService $classroomService; // Untuk mendapatkan data kelas atau validasi kapasitas

    public function __construct(
        StudentRepositoryInterface $studentRepository,
        UserRepositoryInterface $userRepository,
        UserService $userService,
        ClassroomService $classroomService
    ) {
        $this->studentRepository = $studentRepository;
        $this->userRepository = $userRepository;
        $this->userService = $userService;
        $this->classroomService = $classroomService;
    }

    /**
     * Get all students with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->studentRepository->getAll($filters);
    }

    /**
     * Get all students with relations and optional filtering.
     */
    public function getAllWithRelations(array $relations, array $filters = []): Collection
    {
        return $this->studentRepository->getAllWithRelations($relations, $filters);
    }

    /**
     * Get all active students.
     */
    public function getAllActiveStudents(): Collection
    {
        return $this->studentRepository->getAllActive();
    }

    /**
     * Find a student by ID.
     * Mengubah ModelNotFoundException menjadi BusinessLogicException.
     */
    public function findById(int $id): Student
    {
        try {
            return $this->studentRepository->findById($id);
        } catch (ModelNotFoundException $e) {
            throw new BusinessLogicException("Siswa dengan ID {$id} tidak ditemukan.");
        }
    }

    /**
     * Create a new student with user account.
     * Mengelola pembuatan user dan student dalam satu transaksi.
     */
    public function create(array $data): Student
    {
        return DB::transaction(function () use ($data) {
            try {
                // Buat user terlebih dahulu melalui UserService
                $user = $this->userService->create([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => $data['password'],
                    'status' => $data['status'] ?? UserStatus::Active->value,
                    'role' => 'student', // Otomatis set role sebagai 'student'
                    'username' => $data['username'] ?? strtolower(explode('@', $data['email'])[0]),
                ]);

                // Buat student record
                $studentData = [
                    'user_id' => $user->id,
                    'nis' => $data['nis'],
                    'nisn' => $data['nisn'],
                    'birth_place' => $data['birth_place'],
                    'birth_date' => $data['birth_date'],
                    'gender' => $data['gender'],
                    'religion' => $data['religion'],
                    'entry_year' => $data['entry_year'],
                    'full_address' => $data['full_address'] ?? null,
                    'phone_number' => $data['phone_number'] ?? null,
                    'profile_picture' => $data['profile_picture'] ?? null, // dari Controller
                ];
                $student = $this->studentRepository->create($studentData);

                return $student->load('user'); // Muat user relation
            } catch (BusinessLogicException | StudentException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal membuat data siswa: ' . $e->getMessage());
            }
        });
    }

    /**
     * Update student data.
     * Mengelola update student dan user terkait dalam satu transaksi.
     */
    public function update(int $id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            try {
                $student = $this->findById($id); // Validasi siswa ada

                // Hapus gambar profil lama jika ada gambar baru
                if (isset($data['profile_picture']) && $student->profile_picture) {
                    Storage::disk('public')->delete($student->profile_picture);
                }

                // Update user melalui UserService
                $this->userService->update($student->user_id, [
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => $data['password'] ?? null,
                ]);

                // Update student record
                $studentData = [
                    'nis' => $data['nis'],
                    'nisn' => $data['nisn'],
                    'birth_place' => $data['birth_place'],
                    'birth_date' => $data['birth_date'],
                    'gender' => $data['gender'],
                    'religion' => $data['religion'],
                    'entry_year' => $data['entry_year'],
                    'full_address' => $data['full_address'] ?? null,
                    'phone_number' => $data['phone_number'] ?? null,
                    'profile_picture' => $data['profile_picture'] ?? $student->profile_picture,
                ];
                return $this->studentRepository->update($id, $studentData);
            } catch (BusinessLogicException | StudentException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal memperbarui data siswa: ' . $e->getMessage());
            }
        });
    }

    /**
     * Delete a student.
     * Mengelola penghapusan student dan user terkait dalam satu transaksi.
     */
    public function delete(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            try {
                $student = $this->findById($id); // Validasi siswa ada

                // Validasi bisnis sebelum menghapus siswa (misal: apakah punya enrollment aktif)
                if ($this->studentRepository->isEnrolled($id)) {
                    throw StudentException::cannotDeleteEnrolledStudent();
                }
                // Hapus gambar profil jika ada
                if ($student->profile_picture) {
                    Storage::disk('public')->delete($student->profile_picture);
                }

                // Hapus student record
                $deletedStudent = $this->studentRepository->delete($id);

                // Hapus user terkait melalui UserService
                $this->userService->delete($student->user_id);

                return $deletedStudent;
            } catch (BusinessLogicException | StudentException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal menghapus data siswa: ' . $e->getMessage());
            }
        });
    }

    /**
     * Enroll student to a classroom for a specific academic year.
     * Menangani validasi kapasitas kelas.
     */
    public function enrollStudentToClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        return DB::transaction(function () use ($studentId, $classroomId, $academicYearId) {
            try {
                $student = $this->findById($studentId); // Validasi siswa ada
                $classroom = $this->classroomService->findById($classroomId); // Validasi kelas ada

                // Validasi bisnis: Siswa sudah terdaftar di kelas ini untuk tahun ajaran ini
                if ($this->studentRepository->isEnrolledInClassroom($studentId, $classroomId, $academicYearId)) {
                    throw StudentException::alreadyEnrolledInClassroom();
                }

                // Validasi kapasitas kelas
                $currentEnrollments = $this->studentRepository->getStudentsByClassroomAndAcademicYear($classroomId, $academicYearId)->count();
                if ($currentEnrollments >= $classroom->capacity) {
                    throw StudentException::classroomFull();
                }

                return $this->studentRepository->enrollToClassroom($studentId, $classroomId, $academicYearId);
            } catch (BusinessLogicException | StudentException | ModelNotFoundException $e) { // Tangkap ModelNotFound dari ClassroomService
                throw new BusinessLogicException($e->getMessage()); // Ubah menjadi BusinessLogicException
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal mendaftarkan siswa ke kelas: ' . $e->getMessage());
            }
        });
    }

    /**
     * Get student's classrooms.
     */
    public function getStudentClassrooms(int $studentId): Collection
    {
        $this->findById($studentId); // Validasi siswa ada
        return $this->studentRepository->getStudentClassrooms($studentId);
    }

    /**
     * Get student's performance data.
     */
    public function getStudentPerformance(int $studentId): array
    {
        $this->findById($studentId); // Validasi siswa ada
        try {
            return $this->studentRepository->getStudentPerformance($studentId);
        } catch (Throwable $e) {
            throw new BusinessLogicException('Gagal mendapatkan data performa siswa: ' . $e->getMessage());
        }
    }

    /**
     * Get total number of students.
     */
    public function getTotalStudents(): int
    {
        return $this->studentRepository->count();
    }

    /**
     * Update student account settings (jika ada perbedaan dengan `update` utama).
     * Jika sama, metode ini bisa dihapus atau dialihkan ke `update` utama.
     */
    public function updateStudentAccount(int $id, array $data): Student
    {
        // Asumsi ini hanya mengupdate bagian user dari student.
        // Jika ada perubahan pada data Student model, maka harus memanggil studentRepository->update juga.
        // Untuk menjaga konsistensi, lebih baik panggil metode `update` utama.
        $updated = $this->update($id, $data);
        if (!$updated) {
            throw new BusinessLogicException('Gagal memperbarui akun siswa.');
        }
        return $this->findById($id);
    }

    /**
     * Import students from Excel file.
     * Mengelola transaksi, validasi Excel, dan penanganan kesalahan.
     */
    public function importStudents($file): array
    {
        $errors = [];
        DB::beginTransaction();
        try {
            Excel::import(new StudentImport, $file); // StudentImport harus dibuat terpisah
            DB::commit();
            return [
                'success' => true,
                'message' => 'Siswa berhasil diimpor.',
                'errors' => []
            ];
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            DB::rollBack();
            foreach ($e->failures() as $failure) {
                $errors[] = 'Baris ' . $failure->row() . ': ' . implode(', ', $failure->errors());
            }
            throw new BusinessLogicException('Validasi data impor gagal.', null, $errors);
        } catch (Throwable $e) {
            DB::rollBack();
            throw new BusinessLogicException('Gagal mengimpor siswa: ' . $e->getMessage());
        }
    }

    /**
     * Bulk enroll students to classroom.
     * Mengelola validasi kapasitas dan enrollment multiple dalam transaksi.
     */
    public function bulkEnrollToClassroom(array $studentIds, int $classroomId): array
    {
        $results = [];
        return DB::transaction(function () use ($studentIds, $classroomId, &$results) {
            try {
                $classroom = $this->classroomService->findById($classroomId); // Validasi kelas ada

                // Validasi kapasitas kelas secara keseluruhan untuk operasi bulk
                $currentEnrollments = $this->studentRepository->getStudentsByClassroomAndAcademicYear($classroomId, $classroom->academic_year_id)->count();
                $newStudentsCount = count($studentIds);

                if ($currentEnrollments + $newStudentsCount > $classroom->capacity) {
                    throw StudentException::classroomFull();
                }

                foreach ($studentIds as $studentId) {
                    try {
                        // Memanggil enrollStudentToClassroom individu agar validasi unik tetap jalan
                        $this->enrollStudentToClassroom($studentId, $classroomId, $classroom->academic_year_id);
                        $results[$studentId] = true;
                    } catch (BusinessLogicException $e) {
                        // Jika siswa sudah terdaftar atau ada error spesifik, catat tapi lanjutkan
                        $results[$studentId] = false;
                        // Anda bisa juga mengumpulkan pesan error spesifik jika perlu
                        // $results[$studentId] = ['success' => false, 'message' => $e->getMessage()];
                    }
                }
                return ['success' => true, 'results' => $results];
            } catch (BusinessLogicException | ModelNotFoundException $e) {
                throw new BusinessLogicException($e->getMessage());
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal melakukan pendaftaran massal siswa ke kelas: ' . $e->getMessage());
            }
        });
    }

    /**
     * Transfer student to another classroom.
     * Mengelola unenroll dan enroll dalam satu transaksi.
     */
    public function transferStudent(int $studentId, int $fromClassroomId, int $toClassroomId): bool
    {
        return DB::transaction(function () use ($studentId, $fromClassroomId, $toClassroomId) {
            try {
                $student = $this->findById($studentId); // Validasi siswa ada
                $fromClassroom = $this->classroomService->findById($fromClassroomId); // Validasi kelas asal
                $toClassroom = $this->classroomService->findById($toClassroomId); // Validasi kelas tujuan

                // Validasi bahwa siswa terdaftar di kelas asal
                if (!$this->studentRepository->isEnrolledInClassroom($studentId, $fromClassroomId, $fromClassroom->academic_year_id)) {
                    throw StudentException::notEnrolledInClassroom($studentId, $fromClassroomId);
                }

                // Validasi kapasitas kelas tujuan
                $currentEnrollmentsTo = $this->studentRepository->getStudentsByClassroomAndAcademicYear($toClassroomId, $toClassroom->academic_year_id)->count();
                if ($currentEnrollmentsTo >= $toClassroom->capacity) {
                    throw StudentException::classroomFull();
                }

                // Unenroll dari kelas asal
                $this->studentRepository->unenrollFromClassroom($studentId, $fromClassroomId, $fromClassroom->academic_year_id);

                // Enroll ke kelas tujuan
                return $this->studentRepository->enrollToClassroom($studentId, $toClassroomId, $toClassroom->academic_year_id);
            } catch (BusinessLogicException | StudentException | ModelNotFoundException $e) {
                throw new BusinessLogicException($e->getMessage());
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal memindahkan siswa ke kelas lain: ' . $e->getMessage());
            }
        });
    }

    /**
     * Get students not enrolled in a specific classroom (for selection).
     */
    public function getStudentsNotEnrolledInClassroom(int $classroomId, int $academicYearId, ?string $search = null, int $page = 1, int $perPage = 10): array
    {
        // Validasi kelas dan tahun ajaran
        $this->classroomService->findById($classroomId);
        // AcademicYear::findOrFail($academicYearId); // Bisa juga validasi tahun ajaran

        return $this->studentRepository->getStudentsNotEnrolledInClassroom($classroomId, $academicYearId, $search, $page, $perPage);
    }

    /**
     * Remove student from classroom.
     */
    public function removeStudentFromClassroom(int $studentId, int $classroomId): bool
    {
        return DB::transaction(function () use ($studentId, $classroomId) {
            try {
                $student = $this->findById($studentId); // Validasi siswa ada
                $classroomStudent = $student->classrooms()->where('classroom_id', $classroomId)->first();

                if (!$classroomStudent) {
                    throw StudentException::notEnrolledInClassroom($studentId, $classroomId);
                }

                // Unenroll siswa dari kelas
                return $this->studentRepository->unenrollFromClassroom(
                    $studentId,
                    $classroomId,
                    $classroomStudent->pivot->academic_year_id // Ambil academic_year_id dari pivot table
                );
            } catch (BusinessLogicException | StudentException | ModelNotFoundException $e) {
                throw new BusinessLogicException($e->getMessage());
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal mengeluarkan siswa dari kelas: ' . $e->getMessage());
            }
        });
    }

    /**
     * Change the status of a student's account.
     */
    public function changeStudentStatus(int $id, bool $status): Student
    {
        return DB::transaction(function () use ($id, $status) {
            try {
                $student = $this->findById($id); // Validasi siswa ada

                // Gunakan UserService untuk mengubah status user terkait
                // UserService sudah memiliki validasi tidak bisa mengubah status sendiri, dll.
                $this->userService->changeStatus($student->user_id, $status);

                return $this->findById($id); // Ambil ulang data siswa yang sudah diperbarui
            } catch (BusinessLogicException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new BusinessLogicException('Gagal mengubah status siswa: ' . $e->getMessage());
            }
        });
    }

    // Metode statistik bisa tetap ada atau dipindahkan ke ReportService jika semakin kompleks
    private function getEnrolledStudentsCount(): int
    {
        return $this->studentRepository->getEnrolledStudentsCount();
    }

    private function getStudentsByGender(): array
    {
        return $this->studentRepository->getStudentsByGender();
    }

    private function getStudentsByEntryYear(): array
    {
        return $this->studentRepository->getStudentsByEntryYear();
    }

    // Validasi data siswa NIS/NISN/Tahun Masuk/Umur di Service Layer
    // Ini harusnya ada di Service, bukan di FormRequest, karena ini adalah logika bisnis
    public function validateStudentData(array $data, ?int $excludeId = null): void
    {
        // Check NIS uniqueness
        if (isset($data['nis'])) {
            $existingStudent = $this->studentRepository->findByNis($data['nis']);
            if ($existingStudent && ($excludeId === null || $existingStudent->id !== $excludeId)) {
                throw StudentException::nisAlreadyExists($data['nis']);
            }
        }

        // Check NISN uniqueness
        if (isset($data['nisn'])) {
            $existingStudent = $this->studentRepository->findByNisn($data['nisn']);
            if ($existingStudent && ($excludeId === null || $existingStudent->id !== $excludeId)) {
                throw StudentException::nisnAlreadyExists($data['nisn']);
            }
        }

        // Validate entry year
        if (isset($data['entry_year'])) {
            $currentYear = now()->year;
            if ($data['entry_year'] > $currentYear || $data['entry_year'] < ($currentYear - 10)) {
                throw StudentException::invalidEntryYear($data['entry_year']);
            }
        }

        // Validate age if birth_date is provided
        if (isset($data['birth_date'])) {
            $age = now()->diffInYears($data['birth_date']);
            if ($age < 5 || $age > 25) {
                throw StudentException::invalidAge($age);
            }
        }
    }
}
