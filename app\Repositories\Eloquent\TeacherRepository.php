<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\TeacherRepositoryInterface;
// Hapus UserRepositoryInterface dari konstruktor karena UserService sudah handle user repo
// use App\Repositories\Contracts\UserRepositoryInterface;
use App\Enums\RoleEnum; // Dipertahankan untuk filter role
use App\Enums\UserStatus; // Dipertahankan untuk filter status
use App\Models\Teacher;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException; // Import ModelNotFoundException
use Throwable; // Untuk catch generic error dan re-throw sebagai DatabaseException

// Hapus: App\Exceptions\DatabaseException; // Repository akan selalu melempar ModelNotFoundException atau Throwable (untuk diubah ke BusinessLogicException di service)
// Hapus: App\Exceptions\NotFoundException; // Diganti ModelNotFoundException

class TeacherRepository implements TeacherRepositoryInterface
{
    public function __construct(
        private Teacher $teacherModel,
        // Hapus UserRepositoryInterface dari injeksi di Repository, karena TeacherService sudah memilikinya.
        // private UserRepositoryInterface $userRepository
    ) {
    }

    /**
     * Get all teachers with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->teacherModel->with('user')
            ->when(isset($filters['status']), fn($q) => $q->whereHas('user', fn($q) => $q->where('status', $filters['status'])))
            ->when(isset($filters['role']), fn($q) => $q->whereHas('user', fn($q) => $q->whereHas('roles', fn($q) => $q->where('name', $filters['role']))))
            ->when(isset($filters['search']), fn($q) => $q->whereHas('user', $this->getSearchFilter($filters['search'])))
            ->when(isset($filters['gender']), fn($q) => $q->where('gender', $filters['gender']))
            ->orderByDesc('created_at')
            ->get();
    }

    /**
     * Get all active teachers.
     */
    public function getAllActive(): Collection
    {
        return $this->teacherModel->with('user')
            ->whereHas('user', fn($q) => $q->where('status', UserStatus::Active->value)) // Gunakan .value
            ->orderByDesc('created_at')
            ->get();
    }

    /**
     * Find a teacher by ID.
     * Throws ModelNotFoundException if not found.
     */
    public function findById(int $id): Teacher
    {
        return $this->teacherModel->with('user')->findOrFail($id);
    }

    /**
     * Create a new teacher record.
     * Does NOT handle user creation or role assignment.
     */
    public function create(array $data): Teacher
    {
        try {
            $teacher = $this->teacherModel->create([
                'birth_place' => $data['birth_place'],
                'birth_date' => $data['birth_date'],
                'gender' => $data['gender'],
                'user_id' => $data['user_id'], // user_id harus sudah tersedia dari Service
            ]);
            return $teacher;
        } catch (Throwable $e) {
            // Re-throw sebagai RuntimeException atau biarkan naik untuk ditangani Service
            // Jika ini error database, biarkan naik ke Service agar bisa di-rollback
            throw $e;
        }
    }

    /**
     * Update an existing teacher record.
     * Does NOT handle user update or role sync.
     */
    public function update(int $id, array $data): bool
    {
        try {
            $teacher = $this->findById($id); // Akan melempar ModelNotFoundException

            return $teacher->update([
                'birth_place' => $data['birth_place'] ?? $teacher->birth_place,
                'birth_date' => $data['birth_date'] ?? $teacher->birth_date,
                'gender' => $data['gender'] ?? $teacher->gender,
            ]);
        } catch (ModelNotFoundException $e) {
            throw $e; // Biarkan ModelNotFoundException naik ke Service
        } catch (Throwable $e) {
            throw $e; // Biarkan error database naik ke Service untuk di-rollback
        }
    }

    /**
     * Get available teachers with optional filtering.
     * Asumsi 'available' berarti aktif dan belum ditugaskan (tergantung logika aplikasi)
     */
    public function getAvailableTeachers(array $filters): Collection
    {
        return $this->teacherModel->with('user')
            ->whereHas('user', fn($q) => $q->where('status', UserStatus::Active->value))
            ->when(isset($filters['gender']), fn($q) => $q->where('gender', $filters['gender']))
            // Tambahkan kondisi untuk "tersedia" jika ada (misal, tidak sedang dalam assignment penuh)
            ->get();
    }

    /**
     * Get teacher assignments for a specific teacher.
     */
    public function getTeacherAssignments(int $teacherId): Collection
    {
        return $this->findById($teacherId) // Akan melempar ModelNotFoundException
            ->teacherAssignments()
            ->with('subject', 'classroom')
            ->get();
    }

    /**
     * Get teacher schedule for a specific date.
     */
    public function getTeacherSchedule(int $teacherId, string $date): Collection
    {
        return $this->findById($teacherId) // Akan melempar ModelNotFoundException
            ->teacherAssignments()
            ->with([
                'classSchedules' => fn($q) => $q->whereDate('date', $date),
                'subject',
                'classroom',
            ])
            ->get();
    }

    /**
     * Count total number of teachers.
     */
    public function count(): int
    {
        return $this->teacherModel->count();
    }

    /**
     * Delete a teacher record.
     * Does NOT handle user deletion.
     */
    public function delete(int $id): bool
    {
        try {
            $teacher = $this->findById($id); // Akan melempar ModelNotFoundException
            return $teacher->delete();
        } catch (ModelNotFoundException $e) {
            throw $e; // Biarkan ModelNotFoundException naik ke Service
        } catch (Throwable $e) {
            throw $e; // Biarkan error database naik ke Service untuk di-rollback
        }
    }

    /**
     * Check if teacher has active assignments.
     */
    public function hasActiveAssignments(int $teacherId): bool
    {
        $teacher = $this->findById($teacherId); // Akan melempar ModelNotFoundException
        return $teacher->teacherAssignments()
            ->whereHas('academicYear', fn($q) => $q->where('status', 'active')) // Asumsi status tahun ajaran adalah string 'active'
            ->exists();
    }

    /**
     * Get homeroom teachers.
     */
    public function getHomeroomTeachers(): Collection
    {
        return $this->teacherModel->with(['user', 'teacherAssignments.classroom'])
            ->whereHas('teacherAssignments', fn($q) => $q->where('is_homeroom_teacher', true))
            ->whereHas('user', fn($q) => $q->where('status', UserStatus::Active->value)) // Gunakan .value
            ->orderByDesc('created_at')
            ->get();
    }

    /**
     * Helper for search filtering on user attributes.
     */
    private function getSearchFilter(string $searchTerm): callable
    {
        $term = '%' . $searchTerm . '%';

        return fn($q) => $q->where('name', 'like', $term)
            ->orWhere('email', 'like', $term)
            ->orWhere('username', 'like', $term)
            ->orWhere('phone_number', 'like', $term);
    }

    // Metode-metode berikut dihapus karena sudah dipindahkan ke Service Layer atau tidak lagi digunakan:
    // - createTeacherWithUser (sekarang di TeacherService)
    // - updateTeacherWithUser (sekarang di TeacherService)
    // - updateTeacherStatus (sekarang di TeacherService memanggil UserService)
    // - updateUserData (logika user update di UserService)
    // - findByUserId (tidak digunakan di alur utama, jika butuh bisa ditambah kembali)
    // - getAllWithRelations (terlalu generik, lebih baik spesifik atau gunakan eager loading di Service/Controller)
}
