@extends('admin.layouts.app')

@section('title', 'Pengajuan Cuti')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Pengajuan Cuti',
        'breadcrumb' => 'Manaj<PERSON><PERSON>',
    ])

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <!-- Card Header -->
                <div class="card-header border-bottom-dashed">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center gap-3">
                        <!-- Title with Counter -->
                        <div>
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                Daftar @yield('title')
                                <span class="badge bg-primary-subtle text-primary ms-2" id="total-leave-requests">0</span>
                            </h5>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="export-btn">
                                <i class="ri-file-download-line align-bottom"></i> Export
                            </button>
                            <a href="{{ route('admin.leave-requests.create') }}" class="btn btn-primary">
                                <i class="ri-add-line align-bottom"></i> Tambah Pengajuan
                            </a>
                        </div>
                    </div>
                </div>
                <!-- End Card Header -->

                <!-- Filter Section -->
                <div class="card-body border-bottom-dashed border-bottom">
                    <form id="filter-form" class="row g-3">
                        <!-- Status Dropdown -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter-status" class="form-label">Status</label>
                                <select class="form-select" data-choices name="status" id="filter-status">
                                    <option value="">Semua Status</option>
                                    <option value="pending">Menunggu Persetujuan</option>
                                    <option value="approved">Disetujui</option>
                                    <option value="rejected">Ditolak</option>
                                </select>
                            </div>
                        </div>

                        <!-- Leave Type Dropdown -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter-leave-type" class="form-label">Jenis Cuti</label>
                                <select class="form-select" data-choices name="leave_type" id="filter-leave-type">
                                    <option value="">Semua Jenis</option>
                                    <option value="annual">Cuti Tahunan</option>
                                    <option value="sick">Cuti Sakit</option>
                                    <option value="maternity">Cuti Melahirkan</option>
                                    <option value="emergency">Cuti Darurat</option>
                                    <option value="unpaid">Cuti Tanpa Gaji</option>
                                </select>
                            </div>
                        </div>

                        <!-- Date Range -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter-date-from" class="form-label">Dari Tanggal</label>
                                <input type="date" class="form-control" id="filter-date-from" name="date_from">
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter-date-to" class="form-label">Sampai Tanggal</label>
                                <input type="date" class="form-control" id="filter-date-to" name="date_to">
                            </div>
                        </div>

                        <!-- Search Input -->
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="search-input" class="form-label">Cari</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Cari nama karyawan, jenis cuti, alasan..." id="search-input" name="search">
                                    <button class="btn btn-primary" type="button" id="search-button">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- End Filter Section -->

                <!-- Table Section -->
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="datatable" class="table nowrap align-middle" style="width:100%">
                            <thead class="table-light text-muted">
                                <tr class="text-uppercase">
                                    <th>No</th>
                                    <th>Nama Karyawan</th>
                                    <th>Jenis Cuti</th>
                                    <th>Tanggal Mulai</th>
                                    <th>Tanggal Selesai</th>
                                    <th>Durasi</th>
                                    <th>Status</th>
                                    <th>Tanggal Pengajuan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="list">
                                <!-- Data akan diisi via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- End Table Section -->
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style type="text/css">
        .dataTables_length,
        .dataTables_filter {
            display: none !important;
        }
    </style>
@endpush

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.datatables')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script type="text/javascript">
        let table; // Declare the variable in the global scope
        $(document).ready(function() {
            // Initialize DataTable with enhanced configuration
            table = $('#datatable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('admin.leave-requests.index') }}",
                    data: function(d) {
                        return {
                            ...d,
                            status: $('#filter-status').val(),
                            leave_type: $('#filter-leave-type').val(),
                            date_from: $('#filter-date-from').val(),
                            date_to: $('#filter-date-to').val(),
                            search: $('#search-input').val()
                        };
                    },
                    complete: function(response) {
                        $('#total-leave-requests').text(response.responseJSON.recordsTotal || 0);
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'employee_name',
                        name: 'employee.name'
                    },
                    {
                        data: 'leave_type',
                        name: 'leave_type'
                    },
                    {
                        data: 'start_date',
                        name: 'start_date'
                    },
                    {
                        data: 'end_date',
                        name: 'end_date'
                    },
                    {
                        data: 'duration',
                        name: 'duration',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'applied_at',
                        name: 'applied_at'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [7, 'desc'] // Order by applied_at descending
                ],
                language: {
                    processing: "Memuat data...",
                    lengthMenu: "Tampilkan _MENU_ data per halaman",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    infoEmpty: "Menampilkan 0 sampai 0 dari 0 data",
                    infoFiltered: "(difilter dari _MAX_ total data)",
                    search: "Cari:",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    }
                }
            });

            // Filter event handlers
            $('#filter-status, #filter-leave-type, #filter-date-from, #filter-date-to').on('change', function() {
                table.draw();
            });

            $('#search-button').on('click', function() {
                table.draw();
            });

            $('#search-input').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    table.draw();
                }
            });

            // Global deleteItem function for compatibility with button-actions-v2 component
            window.deleteItem = function(element) {
                const url = element.getAttribute('data-url');
                const id = element.getAttribute('data-id');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: "Data pengajuan cuti akan dihapus permanen!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'DELETE',
                            data: {
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire('Berhasil!', response.message, 'success');
                                    table.draw();
                                } else {
                                    Swal.fire('Error!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                const response = xhr.responseJSON;
                                Swal.fire('Error!', response?.message || 'Terjadi kesalahan', 'error');
                            }
                        });
                    }
                });
            };

            // Delete functionality
            $(document).on('click', '.btn-delete', function(e) {
                e.preventDefault();
                const url = $(this).data('url');
                const id = $(this).data('id');

                Swal.fire({
                    title: 'Apakah Anda yakin?',
                    text: "Data pengajuan cuti akan dihapus permanen!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: url,
                            type: 'DELETE',
                            data: {
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire('Berhasil!', response.message, 'success');
                                    table.draw();
                                } else {
                                    Swal.fire('Error!', response.message, 'error');
                                }
                            },
                            error: function(xhr) {
                                const response = xhr.responseJSON;
                                Swal.fire('Error!', response?.message || 'Terjadi kesalahan', 'error');
                            }
                        });
                    }
                });
            });

            // Export functionality
            $('#export-btn').on('click', function() {
                // Implement export functionality if needed
                Swal.fire('Info', 'Fitur export akan segera tersedia', 'info');
            });
        });
    </script>
@endpush
