<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\StudentRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface; // Dipertahankan karena perlu
use App\Enums\UserStatus;
use App\Models\Student;
use App\Models\User; // Student memiliki relasi ke User
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Exceptions\BusinessLogicException; // Untuk validasi findById
use Throwable;

class StudentRepository implements StudentRepositoryInterface
{
    private Student $studentModel;
    private UserRepositoryInterface $userRepository; // Digunakan untuk mencari/memfilter user

    public function __construct(Student $studentModel, UserRepositoryInterface $userRepository)
    {
        $this->studentModel = $studentModel;
        $this->userRepository = $userRepository;
    }

    /**
     * Get all students with optional filters.
     */
    public function getAll(array $filters = []): Collection
    {
        $query = $this->studentModel->with('user');

        if (isset($filters['status'])) {
            $query->whereHas('user', fn($q) => $q->where('status', $filters['status']));
        }

        if (isset($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }

        if (isset($filters['classroom_id'])) {
            $query->whereHas('classrooms', function ($query) use ($filters) {
                $query->where('classroom_id', $filters['classroom_id']);
            });
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->whereHas('user', function ($userQuery) use ($filters) {
                    $userQuery->where('name', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                })
                    ->orWhere('nis', 'like', '%' . $filters['search'] . '%')
                    ->orWhere('nisn', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->get();
    }

    /**
     * Get all active students.
     */
    public function getAllActive(): Collection
    {
        return $this->studentModel->with('user')
            ->whereHas('user', fn($query) => $query->where('status', UserStatus::Active->value))
            ->get();
    }

    /**
     * Find a student by ID.
     * Throws ModelNotFoundException if student is not found.
     */
    public function findById(int $id): Student
    {
        // Menggunakan findOrFail untuk melempar ModelNotFoundException jika tidak ditemukan
        return $this->studentModel->with('user')->findOrFail($id);
    }

    /**
     * Create a new student record.
     * Tidak menangani pembuatan user atau penugasan peran.
     */
    public function create(array $data): Student
    {
        try {
            // Asumsi user_id sudah ada di $data dari StudentService
            $student = $this->studentModel->create($data);
            return $student;
        } catch (Throwable $e) {
            // Biarkan error naik ke Service untuk ditangani transaksi
            throw $e;
        }
    }

    /**
     * Update an existing student record.
     * Tidak menangani update user atau penugasan peran.
     */
    public function update(int $id, array $data): bool
    {
        try {
            $student = $this->findById($id); // Akan melempar ModelNotFoundException
            return $student->update($data);
        } catch (ModelNotFoundException $e) {
            throw $e; // Biarkan ModelNotFoundException naik ke Service
        } catch (Throwable $e) {
            throw $e; // Biarkan error database naik ke Service
        }
    }

    /**
     * Delete a student record.
     * Tidak menangani penghapusan user.
     */
    public function delete(int $id): bool
    {
        try {
            $student = $this->findById($id); // Akan melempar ModelNotFoundException
            return $student->delete();
        } catch (ModelNotFoundException $e) {
            throw $e; // Biarkan ModelNotFoundException naik ke Service
        } catch (Throwable $e) {
            throw $e; // Biarkan error database naik ke Service
        }
    }

    /**
     * Enroll student to a classroom for a specific academic year.
     */
    public function enrollToClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        $student = $this->findById($studentId);
        $student->classrooms()->attach($classroomId, [
            'academic_year_id' => $academicYearId,
        ]);
        return true;
    }

    /**
     * Remove student from a classroom.
     */
    public function unenrollFromClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        $student = $this->findById($studentId);
        return $student->classrooms()
            ->wherePivot('classroom_id', $classroomId)
            ->wherePivot('academic_year_id', $academicYearId)
            ->detach() > 0;
    }

    /**
     * Get student's classrooms.
     */
    public function getStudentClassrooms(int $studentId): Collection
    {
        $student = $this->findById($studentId);
        return $student->classrooms()->with('academicYear')->get();
    }

    /**
     * Check if student is enrolled in a classroom for a specific academic year.
     */
    public function isEnrolledInClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        $student = $this->findById($studentId);
        return $student->classrooms()
            ->wherePivot('classroom_id', $classroomId)
            ->wherePivot('academic_year_id', $academicYearId)
            ->exists();
    }

    /**
     * Get student's academic performance data.
     */
    public function getStudentPerformance(int $studentId): array
    {
        $this->findById($studentId); // Memastikan siswa ada
        // Placeholder untuk logika performa siswa
        return [
            'student_id' => $studentId,
            'attendance_rate' => 0,
            'average_grade' => 0,
            'completed_assignments' => 0,
        ];
    }

    /**
     * Get all students with relations.
     */
    public function getAllWithRelations(array $relations, array $filters = []): Collection
    {
        $query = $this->studentModel->with($relations);

        if (isset($filters['status'])) {
            $query->whereHas('user', fn($q) => $q->where('status', $filters['status']));
        }

        if (isset($filters['classroom_id'])) {
            $query->whereHas('classrooms', fn($q) => $q->where('classroom_id', $filters['classroom_id']));
        }

        if (isset($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->whereHas('user', function ($userQuery) use ($filters) {
                    $userQuery->where('name', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                })
                    ->orWhere('nis', 'like', '%' . $filters['search'] . '%')
                    ->orWhere('nisn', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->get();
    }

    /**
     * Get total count of students.
     */
    public function count(): int
    {
        return $this->studentModel->count();
    }

    /**
     * Find student by NIS.
     */
    public function findByNis(string $nis): ?Student
    {
        return $this->studentModel->with('user')->where('nis', $nis)->first();
    }

    /**
     * Find student by NISN.
     */
    public function findByNisn(string $nisn): ?Student
    {
        return $this->studentModel->with('user')->where('nisn', $nisn)->first();
    }

    /**
     * Check if student is enrolled in any classroom for active academic year.
     */
    public function isEnrolled(int $studentId): bool
    {
        $student = $this->findById($studentId);
        return $student->isEnrolled(); // Asumsi ada method isEnrolled() di model Student
    }

    /**
     * Get students by classroom and academic year.
     */
    public function getStudentsByClassroomAndAcademicYear(int $classroomId, int $academicYearId): Collection
    {
        return $this->studentModel->with(['user', 'classrooms'])
            ->whereHas('classrooms', function ($query) use ($classroomId, $academicYearId) {
                $query->where('classroom_id', $classroomId)
                    ->where('academic_year_id', $academicYearId);
            })
            ->get();
    }

    /**
     * Get students not enrolled in a specific classroom (for selection).
     * Ini bisa dipindahkan dari controller.
     */
    public function getStudentsNotEnrolledInClassroom(int $classroomId, int $academicYearId, ?string $search = null, int $page = 1, int $perPage = 10): array
    {
        $query = $this->studentModel->whereDoesntHave('classrooms', function ($query) use ($classroomId, $academicYearId) {
            $query->where('classroom_students.academic_year_id', $academicYearId);
        })->whereHas('user', function ($query) {
            $query->where('status', UserStatus::Active->value);
        });

        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('user', function ($userQuery) use ($search) {
                    $userQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                })
                    ->orWhere('nis', 'like', "%{$search}%")
                    ->orWhere('nisn', 'like', "%{$search}%");
            });
        }

        $totalRecords = $query->count();
        $lastPage = ceil($totalRecords / $perPage);

        $students = $query->with('user')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get()
            ->map(function ($student) {
                return [
                    'id' => $student->id,
                    'name' => $student->user->name,
                    'nis' => $student->nis,
                    'nisn' => $student->nisn,
                    'email' => $student->user->email,
                ];
            });

        return [
            'data' => $students,
            'current_page' => (int) $page,
            'last_page' => $lastPage,
            'total' => $totalRecords,
        ];
    }


    // Metode statistik bisa dipindahkan ke sini dari service
    public function getStudentsByGender(): array
    {
        $students = $this->studentModel->all(); // Mengambil semua untuk grouping
        return [
            'male' => $students->where('gender', 'male')->count(),
            'female' => $students->where('gender', 'female')->count(),
        ];
    }

    public function getStudentsByEntryYear(): array
    {
        return $this->studentModel->all()
            ->groupBy('entry_year')
            ->map(fn($students) => $students->count())
            ->toArray();
    }

    public function getEnrolledStudentsCount(): int
    {
        return $this->studentModel->whereHas('classrooms', function ($q) {
            $q->whereHas('academicYear', fn($ayQ) => $ayQ->where('status', 'active'));
        })->count();
    }
}
