<?php

namespace App\Repositories\Contracts;

use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Exceptions\BusinessLogicException; // Untuk findById jika student tidak ditemukan

interface StudentRepositoryInterface
{
    /**
     * Get all students with optional filters.
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Get all active students.
     */
    public function getAllActive(): Collection;

    /**
     * Find a student by ID.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException Jika siswa tidak ditemukan.
     */
    public function findById(int $id): Student;

    /**
     * Create a new student record.
     * @throws \Throwable Jika terjadi kesalahan pada level database.
     */
    public function create(array $data): Student;

    /**
     * Update an existing student record.
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     * @throws \Throwable Jika terjadi kesalahan pada level database.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a student record.
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     * @throws \Throwable Jika terjadi kesalahan pada level database.
     */
    public function delete(int $id): bool;

    /**
     * Enroll student to a classroom for a specific academic year.
     */
    public function enrollToClassroom(int $studentId, int $classroomId, int $academicYearId): bool;

    /**
     * Remove student from a classroom.
     */
    public function unenrollFromClassroom(int $studentId, int $classroomId, int $academicYearId): bool;

    /**
     * Get student's classrooms.
     */
    public function getStudentClassrooms(int $studentId): Collection;

    /**
     * Check if student is enrolled in a classroom for a specific academic year.
     */
    public function isEnrolledInClassroom(int $studentId, int $classroomId, int $academicYearId): bool;

    /**
     * Get student's academic performance data (placeholder).
     */
    public function getStudentPerformance(int $studentId): array;

    /**
     * Get all students with relations and optional filters.
     */
    public function getAllWithRelations(array $relations, array $filters = []): Collection;

    /**
     * Get total count of students.
     */
    public function count(): int;

    /**
     * Find student by NIS.
     */
    public function findByNis(string $nis): ?Student;

    /**
     * Find student by NISN.
     */
    public function findByNisn(string $nisn): ?Student;

    /**
     * Check if student is enrolled in any classroom for active academic year.
     */
    public function isEnrolled(int $studentId): bool;

    /**
     * Get students by classroom and academic year.
     */
    public function getStudentsByClassroomAndAcademicYear(int $classroomId, int $academicYearId): Collection;

    /**
     * Get students not enrolled in a specific classroom (for selection).
     */
    public function getStudentsNotEnrolledInClassroom(int $classroomId, int $academicYearId, ?string $search = null, int $page = 1, int $perPage = 10): array;

    /**
     * Get students grouped by gender.
     */
    public function getStudentsByGender(): array;

    /**
     * Get students grouped by entry year.
     */
    public function getStudentsByEntryYear(): array;

    /**
     * Get count of students enrolled in any classroom for the active academic year.
     */
    public function getEnrolledStudentsCount(): int;
}
